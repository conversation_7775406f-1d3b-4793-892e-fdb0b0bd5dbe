import { ArrowLeftOutlined, SearchOutlined } from '@ant-design/icons';
import { Input, Checkbox, Flex, Carousel, Select, Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import { DocumentTabName } from './documentThumbnailsView';
import { ThumbnailInfo } from '../utils/thumbnailInfo';
import useScopesStore from './store/useScopesStore';

const Container = styled.div<{ isThumbnailsOpen: boolean }>`
  background: ${themeTokens.textLight};
  padding: ${({ isThumbnailsOpen }) => (isThumbnailsOpen ? '20px' : '2px 20px')};
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const SearchBar = styled(Input)`
  width: 55%;
  min-width: 300px;
  border: 1px solid #201a22;
`;

const FilterContainer = styled.div`
  display: flex;
  align-items: center;
  .ant-select-selector {
    padding-left: 6px !important;
  }
`;

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  overflow: auto;
`;

const StyledImageContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  gap: 2px;
  padding: 10px;
  text-align: center;
  width: 150px;
`;

const StyledImage = styled.img<{ isActive: boolean }>`
  height: 100%;
  width: 100%;
  object-fit: contain;
  max-height: 70px;
  border: 1px solid ${({ isActive }) => (isActive ? themeTokens.primaryColor : '#4e4e4e')};
`;

const SearchHeaderContainer = styled.div`
  width: 100%;
  padding: 0 40px;
`;

const SearchResultsText = styled.div`
  font-size: 14px;
  margin-top: 8px;
  margin-left: 4px;
`;

const BoldText = styled.span`
  font-weight: 700;
`;

const SearchContainer = styled(Flex)`
  width: 100%;
`;

const filterType = {
  all: 'All',
  currentScope: 'Current Scope'
};

interface FileViewerProps {
  isThumbnailsOpen: boolean;
  drawingsThumbnails: ThumbnailInfo[];
  specificationsThumbnails: ThumbnailInfo[];
}

const FileViewer: React.FC<FileViewerProps> = ({
  isThumbnailsOpen,
  drawingsThumbnails,
  specificationsThumbnails
}) => {
  const [searchText, setSearchText] = useState<string>('');
  const [showDrawings, setShowDrawings] = useState<boolean>(true);
  const [showSpecs, setShowSpecs] = useState<boolean>(false);
  const [drawingsFilterType, setDrawingsFilterType] = useState<string>(filterType.all);
  const [specificationsFilterType, setSpecificationsFilterType] = useState<string>(filterType.all);
  const [filteredThumbnails, setFilteredThumbnails] = useState<ThumbnailInfo[]>([]);
  const { selectedThumbnailInfo, setSelectedThumbnailInfo } = useScopesStore();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  useEffect(() => {
    const thumbnails = [...drawingsThumbnails, ...specificationsThumbnails];

    setFilteredThumbnails(thumbnails);
  }, [drawingsThumbnails, specificationsThumbnails]);

  //Do not delete will reuse for implementing FE search
  const onSearchHandler = async () => {
    // if (!searchText || searchText.trim().length === 0) {
    //   setFilteredThumbnails(thumbnails);
    //   return;
    // }
    // const res = await findThumbnailsWithText(
    //   searchText,
    //   thumbnails[0]?.document?.presignedUrl,
    //   thumbnails[0]?.sheet?.pageNumber
    // );
    // const filteredThumbnails = thumbnails.filter(thumbnail => {
    //   return res.some(result => result.page === thumbnail?.sheet?.pageNumber);
    // });
    // setFilteredThumbnails(filteredThumbnails);
  };

  return (
    <Container isThumbnailsOpen={isThumbnailsOpen}>
      {isThumbnailsOpen ? (
        <Flex vertical gap={30}>
          <SearchHeaderContainer>
            <Flex align='center' justify='space-between' gap={24}>
              <SearchContainer align='center' gap={20}>
                <SearchBar
                  placeholder='Search here'
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={handleSearchChange}
                  size='large'
                  variant='outlined'
                />
                <Button
                  variant='solid'
                  type='primary'
                  size='middle'
                  style={{ width: 'fit-content' }}
                  onClick={onSearchHandler}
                >
                  Search
                </Button>
              </SearchContainer>
              <Flex align='center' gap={10}>
                <FilterContainer>
                  <Checkbox
                    checked={showDrawings}
                    onChange={e => setShowDrawings(e.target.checked)}
                  />
                  <Select
                    variant='borderless'
                    options={[
                      { label: 'All', value: filterType.all },
                      { label: 'Current Scope', value: filterType.currentScope }
                    ]}
                    dropdownStyle={{ width: 'fit-content' }}
                    value={`${DocumentTabName.drawings} (${drawingsFilterType})`}
                    onChange={val => setDrawingsFilterType(val)}
                  />
                </FilterContainer>
                <FilterContainer>
                  <Checkbox checked={showSpecs} onChange={e => setShowSpecs(e.target.checked)} />
                  <Select
                    variant='borderless'
                    options={[
                      { label: 'All', value: filterType.all },
                      { label: 'Current Scope', value: filterType.currentScope }
                    ]}
                    dropdownStyle={{ width: 'fit-content' }}
                    value={`${DocumentTabName.specs} (${specificationsFilterType})`}
                    onChange={val => setSpecificationsFilterType(val)}
                  />
                </FilterContainer>
              </Flex>
            </Flex>
            {/* <SearchResultsText>
              <BoldText>{filteredThumbnails.length}</BoldText> items found{' '}
                <span>
                  - <BoldText>10</BoldText> drawings{' '}
                </span>
                <span>
                  and <BoldText>2</BoldText> specifications
                </span> 
            </SearchResultsText> */}
          </SearchHeaderContainer>
          <GridContainer>
            {filteredThumbnails?.map(file => (
              <StyledImageContainer key={file?.sheet?.sheetId}>
                <StyledImage
                  src={file.thumbnail}
                  alt={file?.sheet?.title}
                  onClick={() => setSelectedThumbnailInfo(file)}
                  isActive={file.sheet.sheetId === selectedThumbnailInfo?.sheet?.sheetId}
                />
                <div>{file?.sheet?.sheetNumber}</div>
                <div>{file?.sheet?.title}</div>
              </StyledImageContainer>
            ))}
          </GridContainer>
        </Flex>
      ) : (
        <Carousel
          dots={false}
          arrows={true}
          infinite={false}
          slidesToShow={3}
          slidesToScroll={1}
          key={filteredThumbnails ? filteredThumbnails.length : 'empty'}
          prevArrow={<ArrowLeftOutlined style={{ color: 'black', fontSize: '24px' }} />}
        >
          {filteredThumbnails?.map(file => (
            <StyledImageContainer key={file?.sheet?.sheetId}>
              <StyledImage
                isActive={file.sheet.sheetId === selectedThumbnailInfo?.sheet?.sheetId}
                src={file.thumbnail}
                alt={file?.sheet?.title}
                onClick={() => setSelectedThumbnailInfo(file)}
              />
              <div>{file?.sheet?.sheetNumber}</div>
              <div>{file?.sheet?.title}</div>
            </StyledImageContainer>
          ))}
        </Carousel>
      )}
    </Container>
  );
};

export default FileViewer;
