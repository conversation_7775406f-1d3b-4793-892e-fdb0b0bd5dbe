import {
  SearchOutlined,
  DeleteOutlined,
  EyeOutlined,
  SyncOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Button,
  Dropdown,
  Empty,
  Flex,
  Form,
  Input,
  List,
  Pagination,
  Select,
  Tooltip,
  App,
  Table,
  Tag
} from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { BsGrid3X3Gap, BsList } from 'react-icons/bs';
import { FaPlus } from 'react-icons/fa6';
import { LiaSlidersHSolid } from 'react-icons/lia';
import { LuArrowDownUp } from 'react-icons/lu';
import { useNavigate } from 'react-router-dom';
import { GetAllProjectsStatusEnum, PageProjectDTO, ProjectDTO } from 'src/api';
import { companyAPI, projectAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import CreateProject from './createProject';
import useGlobalStore from '../../store/useGlobalStore';
import CustomIcon from '../common/customIcon';
import HasPermission from '../guards/HasPermission';
import { queryKeys, DocProcessingJobStatus } from '../utils/constant';
import { extractErrorMessage } from '../utils/errorHandler';
import { UserPermission } from '../utils/permissions';
import ProjectListItem from './components/ProjectListItem';
import ExportFile from '../common/exportFile';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 24px;
  font-family: 'Inter';
`;

const Content = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 24px 0;
  background: ${themeTokens.pageBg};
`;

const StyledButton = styled(Button)`
  font-size: 20px;
  line-height: 28px;
`;

const StyledInput = styled(Input)`
  width: 300px;
  border: 1px solid #656565;
  border-radius: 10px;
`;

const FilterDropdownContainer = styled.div`
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
`;

const ViewToggleContainer = styled.div`
  display: flex;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
`;

const ViewToggleButton = styled.div<{ active: boolean }>`
  padding: 8px 12px;
  cursor: pointer;
  background-color: ${({ active }) => (active ? themeTokens.primaryColor : 'white')};
  color: ${({ active }) => (active ? 'white' : '#666')};
  border-right: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:last-child {
    border-right: none;
  }

  &:hover {
    background-color: ${({ active }) => (active ? themeTokens.primaryColor : '#f5f5f5')};
  }
`;

const StyledTable = styled(Table)`
  margin-bottom: 20px;
  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: white;
    font-weight: 600;
  }

  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  /*Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;

interface FilterState {
  companyId?: number;
  location?: string;
  status?: GetAllProjectsStatusEnum;
  name?: string;
  owner?: string;
  documentsStatus?: string;
}

type ViewType = 'list' | 'table';

enum ProjectDisplayStatus {
  processing = 'Processing',
  processed = 'Processed',
  error = 'Error'
}

const ProjectsList: React.FC = () => {
  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState<boolean>(false);
  const [isFilterDropdownOpen, setIsFilterDropdownOpen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [filters, setFilters] = useState<FilterState>({});
  const [tableFilters, setTableFilters] = useState<FilterState>({});
  const [isSortAscending, setIsSortAscending] = useState<boolean>(false);
  const [viewType, setViewType] = useState<ViewType>('list');

  const [filterForm] = Form.useForm();
  const {
    setSelectedProjectId,
    currentUser,
    setSelectedVersion,
    selectedProjectId,
    documentProcessingListeners
  } = useGlobalStore();
  const queryClient = useQueryClient();
  const { modal, notification } = App.useApp();
  const navigate = useNavigate();

  // Helper function to get project documents status (same logic as ProjectListItem)
  const getProjectDocumentsStatus = (projectId?: number) => {
    if (documentProcessingListeners && projectId) {
      const status = documentProcessingListeners[projectId];
      switch (status) {
        case DocProcessingJobStatus.inProgress:
        case DocProcessingJobStatus.queued:
          return {
            status: ProjectDisplayStatus.processing,
            icon: <SyncOutlined spin />,
            color: 'processing'
          };
        case DocProcessingJobStatus.failed:
          return {
            status: ProjectDisplayStatus.error,
            icon: <CloseCircleOutlined />,
            color: 'error'
          };
        case DocProcessingJobStatus.completed:
          return {
            status: ProjectDisplayStatus.processed,
            icon: <CheckCircleOutlined />,
            color: 'success'
          };
        default:
          return null;
      }
    }
    return null;
  };

  // Table columns definition
  const tableColumns: any[] = [
    {
      title: 'Project Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: ProjectDTO) => (
        <Tooltip title={text}>
          <a
            onClick={() => {
              onProjectClickHandler(record?.projectId);
              navigate(
                (record?.scopeCount ? record?.scopeCount : 0) > 0
                  ? `${record?.projectId}/scopes`
                  : `${record?.projectId}/documents`
              );
            }}
            style={{ color: themeTokens.linkBlue }}
          >
            {text}
          </a>
        </Tooltip>
      ),
      sorter: true,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder='Search project name'
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              setTableFilters(prev => ({ ...prev, name: selectedKeys[0] as string }));
              setCurrentPage(0);
              confirm();
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              type='primary'
              onClick={() => {
                setTableFilters(prev => ({ ...prev, name: selectedKeys[0] as string }));
                setCurrentPage(0);
                confirm();
              }}
              size='small'
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button
              onClick={() => {
                setTableFilters(prev => ({ ...prev, name: undefined }));
                setCurrentPage(0);
                if (clearFilters) clearFilters();
              }}
              size='small'
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </div>
        </div>
      ),
      width: '25%'
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      render: (text: string) => (
        <Tooltip title={text}>
          <span
            style={{
              maxWidth: '200px',
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {text || '-'}
          </span>
        </Tooltip>
      ),
      sorter: true,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder='Search location'
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              setTableFilters(prev => ({ ...prev, location: selectedKeys[0] as string }));
              setCurrentPage(0);
              confirm();
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              type='primary'
              onClick={() => {
                setTableFilters(prev => ({ ...prev, location: selectedKeys[0] as string }));
                setCurrentPage(0);
                confirm();
              }}
              size='small'
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button
              onClick={() => {
                setTableFilters(prev => ({ ...prev, location: undefined }));
                setCurrentPage(0);
                if (clearFilters) clearFilters();
              }}
              size='small'
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </div>
        </div>
      ),
      width: '20%'
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
      render: (text: string) => (
        <Tooltip title={text}>
          <span
            style={{
              maxWidth: '150px',
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {text || '-'}
          </span>
        </Tooltip>
      ),
      sorter: true,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder='Search owner'
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              setTableFilters(prev => ({ ...prev, owner: selectedKeys[0] as string }));
              setCurrentPage(0);
              confirm();
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              type='primary'
              onClick={() => {
                setTableFilters(prev => ({ ...prev, owner: selectedKeys[0] as string }));
                setCurrentPage(0);
                confirm();
              }}
              size='small'
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button
              onClick={() => {
                setTableFilters(prev => ({ ...prev, owner: undefined }));
                setCurrentPage(0);
                if (clearFilters) clearFilters();
              }}
              size='small'
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </div>
        </div>
      ),
      width: 120
    },
    {
      title: 'Documents Status',
      dataIndex: 'projectId',
      key: 'documentsStatus',
      render: (_: unknown, record: ProjectDTO) => {
        const projectDocumentsStatus = getProjectDocumentsStatus(record.projectId);
        if (projectDocumentsStatus) {
          return (
            <Tag
              icon={projectDocumentsStatus.icon}
              color={projectDocumentsStatus.color}
              style={{ cursor: 'pointer' }}
            >
              {projectDocumentsStatus.status}
            </Tag>
          );
        }
        return <Tag color='default'>-</Tag>;
      },
      width: 150,
      filters: [
        { text: 'Processing', value: ProjectDisplayStatus.processing },
        { text: 'Processed', value: ProjectDisplayStatus.processed },
        { text: 'Error', value: ProjectDisplayStatus.error },
        { text: 'No Status', value: 'none' }
      ],
      onFilter: (value: string | number | boolean, record: ProjectDTO) => {
        // Keep frontend filtering for documents status since it's based on client-side data
        const projectDocumentsStatus = getProjectDocumentsStatus(record.projectId);
        if (value === 'none') {
          return !projectDocumentsStatus;
        }
        return projectDocumentsStatus?.status === value;
      },
      filterMultiple: true
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => (
        <Tooltip title={text}>
          <span
            style={{
              maxWidth: '200px',
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {text || '-'}
          </span>
        </Tooltip>
      ),
      width: '20%'
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_: unknown, record: ProjectDTO) => (
        <>
          <Tooltip trigger={'hover'} title='View Project'>
            <EyeOutlined
              className='action-icon'
              onClick={() => {
                onProjectClickHandler(record?.projectId);
                navigate(`${record?.projectId}`);
              }}
            />
          </Tooltip>
          <Tooltip trigger={'hover'} title='Delete Project'>
            <DeleteOutlined
              className='action-icon'
              onClick={() => handleDeleteProject(record?.projectId)}
            />
          </Tooltip>
        </>
      )
    }
  ];

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchText]);

  // API Queries
  const { data: companiesList, isPending: isCompaniesLoading } = useQuery({
    queryKey: [queryKeys.companiesList],
    queryFn: () => companyAPI.getAllCompanies(0, 1000),
    select: res => res.data.content,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const { data: projectsData, isPending } = useQuery({
    queryKey: [
      queryKeys.allProjects,
      currentPage,
      pageSize,
      debouncedSearchText,
      filters.companyId,
      filters.location,
      filters.status,
      tableFilters.name,
      tableFilters.owner,
      tableFilters.documentsStatus,
      isSortAscending
    ],
    queryFn: () =>
      projectAPI.getAllProjects(
        filters.companyId,
        // Combine search text with table name filter
        tableFilters.name || debouncedSearchText,
        undefined,
        tableFilters.location || filters.location,
        filters.status,
        tableFilters.owner,
        currentPage,
        pageSize,
        ['createdAt', isSortAscending ? 'asc' : 'desc']
      ),
    select: res => res.data as PageProjectDTO
  });

  const handleCreateProject = useCallback(() => setIsCreateProjectModalOpen(true), []);
  const onCreateProjectModalClose = useCallback(() => setIsCreateProjectModalOpen(false), []);

  const onProjectClickHandler = useCallback(
    (projectId?: number) => {
      if (projectId) {
        if (projectId.toString() !== selectedProjectId) setSelectedVersion(null);
        setSelectedProjectId(projectId.toString());
      }
    },
    [setSelectedProjectId, setSelectedVersion, selectedProjectId]
  );

  // Delete project mutation
  const { mutate: deleteProject } = useMutation({
    mutationFn: (projectId: number) => projectAPI.softDeleteProject(projectId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
      notification.success({ message: 'Project deleted successfully' });
    },
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Project deletion Failed') });
    }
  });

  // Handle delete project
  const handleDeleteProject = useCallback(
    (projectId?: number) => {
      if (!projectId) return;

      modal.confirm({
        title: 'Delete Project',
        content: 'Are you sure you want to delete this project?',
        okText: 'Yes, Delete',
        okType: 'danger',
        cancelText: 'Cancel',
        onOk: () => {
          deleteProject(projectId);
        }
      });
    },
    [deleteProject, modal]
  );

  const handleFilterReset = useCallback(() => {
    filterForm.resetFields();
    setFilters({});
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const handleFilterApply = useCallback(() => {
    const values = filterForm.getFieldsValue();
    setFilters(values);
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const onSearchHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
    setCurrentPage(0);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page - 1);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter && sorter.field) {
      const isAscending = sorter.order === 'ascend';
      setIsSortAscending(isAscending);
    }

    // Reset to first page when filters change
    setCurrentPage(0);
  };

  const FilterFormContent = () => (
    <Form form={filterForm} layout='vertical' initialValues={filters}>
      {!currentUser?.companyId ? (
        <Form.Item label='Company' name='companyId'>
          <Select
            placeholder='Select Company'
            allowClear
            loading={isCompaniesLoading}
            options={(companiesList || [])?.map(company => ({
              label: company.name,
              value: company.id
            }))}
          />
        </Form.Item>
      ) : (
        <></>
      )}
      <Form.Item label='Location' name='location'>
        <Input placeholder='Enter location' />
      </Form.Item>
      <Form.Item label='Status' name='status'>
        <Select
          placeholder='Select Status'
          allowClear
          options={[
            { label: 'Active', value: 'ACTIVE' },
            { label: 'Inactive', value: 'INACTIVE' },
            { label: 'Completed', value: 'COMPLETED' },
            { label: 'Pending', value: 'PENDING' }
          ]}
        />
      </Form.Item>
      <Flex justify='space-between' align='center'>
        <Button onClick={handleFilterReset}>Reset</Button>
        <Button type='primary' onClick={handleFilterApply}>
          Apply Filters
        </Button>
      </Flex>
    </Form>
  );

  return (
    <Container>
      <Content>
        <Flex vertical>
          <HeaderContainer>
            <Header>Projects</Header>
            <Flex gap={24} align='center'>
              <StyledInput
                size='large'
                placeholder='Search here'
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={onSearchHandler}
              />
              <ViewToggleContainer>
                <ViewToggleButton active={viewType === 'list'} onClick={() => setViewType('list')}>
                  <BsGrid3X3Gap size={16} />
                </ViewToggleButton>
                <ViewToggleButton
                  active={viewType === 'table'}
                  onClick={() => setViewType('table')}
                >
                  <BsList size={16} />
                </ViewToggleButton>
              </ViewToggleContainer>
              <Tooltip title='Sort'>
                <LuArrowDownUp
                  cursor='pointer'
                  onClick={() => setIsSortAscending(!isSortAscending)}
                  size={20}
                />
              </Tooltip>
              <Tooltip title='Filter'>
                <Dropdown
                  trigger={['click']}
                  placement='bottomRight'
                  open={isFilterDropdownOpen}
                  onOpenChange={setIsFilterDropdownOpen}
                  dropdownRender={() => (
                    <FilterDropdownContainer>
                      <Flex vertical gap={16}>
                        <h3>Filter Projects</h3>
                        <FilterFormContent />
                      </Flex>
                    </FilterDropdownContainer>
                  )}
                >
                  <LiaSlidersHSolid cursor='pointer' size={22} />
                </Dropdown>
              </Tooltip>
              {/* {viewType === 'table' && (
                <ExportFile
                  data={(projectsData?.content || []) as ProjectDTO[]}
                  columns={[
                    { label: 'Project Name', key: 'name' },
                    { label: 'Location', key: 'location' },
                    { label: 'Owner', key: 'owner' },
                    { label: 'Status', key: 'status' },
                    { label: 'Description', key: 'description' }
                  ]}
                  filename='projects'
                />
              )} */}
              <HasPermission requiredPermissions={[UserPermission.CREATE_PROJECT]}>
                <StyledButton
                  onClick={handleCreateProject}
                  icon={<CustomIcon Icon={FaPlus} />}
                  type='primary'
                >
                  Add Project
                </StyledButton>
              </HasPermission>
            </Flex>
          </HeaderContainer>
          {viewType === 'list' ? (
            <List
              grid={{
                gutter: 25,
                xs: 1,
                sm: 1,
                md: 2,
                lg: 2,
                xl: 2,
                xxl: 2
              }}
              loading={isPending}
              dataSource={(projectsData?.content || []) as ProjectDTO[]}
              locale={{ emptyText: <Empty description='No projects found' /> }}
              renderItem={item => (
                <List.Item>
                  <ProjectListItem
                    project={item}
                    onProjectClickHandler={onProjectClickHandler}
                    handleDeleteProject={handleDeleteProject}
                  />
                </List.Item>
              )}
            />
          ) : (
            <StyledTable
              columns={tableColumns}
              dataSource={(projectsData?.content || []) as ProjectDTO[]}
              loading={isPending}
              pagination={false}
              rowKey='projectId'
              bordered
              rowHoverable
              locale={{ emptyText: <Empty description='No projects found' /> }}
              scroll={{ y: 'calc(100vh - 300px)' }}
              onChange={handleTableChange}
            />
          )}
          {projectsData && projectsData.totalElements && projectsData.totalElements > 0 ? (
            <Flex justify='center' align='center'>
              <Pagination
                current={currentPage + 1}
                pageSize={pageSize}
                total={projectsData.totalElements}
                showSizeChanger
                pageSizeOptions={['5', '10', '20', '50']}
                onChange={handlePageChange}
                showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} projects`}
              />
            </Flex>
          ) : null}
        </Flex>
      </Content>
      {isCreateProjectModalOpen && (
        <CreateProject open={isCreateProjectModalOpen} onClose={onCreateProjectModalClose} />
      )}
    </Container>
  );
};

export default ProjectsList;
