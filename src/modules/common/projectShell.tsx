import { Route, Routes } from 'react-router-dom';
import Layout from './layout';
import CompaniesList from '../company/companiesList';
import CompanyDetail from '../company/companyDetails';
import NotFound from '../error/NotFound';
import AuthGuard from '../guards/authGuard';
import HasPermission from '../guards/HasPermission';
import WyreAIAdminGuard from '../guards/WyreAIAdminGuard';
import DocumentsList from '../projectDocuments/documentsList';
import ProjectDetail from '../projects/projectDetail';
import ProjectsList from '../projects/projectsList';
import Scopes from '../scopes';
import ScopeInfo from '../scopes/scopeInfo';
import UserDetail from '../user/userDetails';
import UsersList from '../user/usersList';
import { appRoutes } from '../utils/constant';
import { UserPermission } from '../utils/permissions';
import WyreAIAdmin from '../wyreAIAdmin';
import Metrics from '../wyreAIAdmin/metrics';
import Projects from '../wyreAIAdmin/projects';
import UserManagement from '../wyreAIAdmin/userManagement';

export const ProjectShell: React.FC = () => {
  return (
    <Routes>
      <Route
        element={
          <AuthGuard>
            <Layout />
          </AuthGuard>
        }
      >
        <Route path={appRoutes.projects}>
          <Route index element={<ProjectsList />} />
          <Route path=':projectId/'>
            <Route index element={<ProjectDetail />} />
            <Route path={appRoutes.scopes} element={<Scopes />}>
              <Route path=':scopeId/' element={<ScopeInfo />} />
            </Route>
            <Route path={appRoutes.documents} element={<DocumentsList />} />
            <Route path={appRoutes.bidSheets} element={<div>Bid Sheets</div>} />
            <Route path={appRoutes.dashboard} element={<div>Dashboard</div>} />
          </Route>
        </Route>
        <Route path={appRoutes.enterprise} element={<div> Enterprise </div>} />
        <Route path={appRoutes.user}>
          <Route index element={<UsersList />} />
          <Route path=':userId/' element={<UserDetail />} />
        </Route>
        <Route path={appRoutes.company}>
          <Route
            index
            element={
              <HasPermission requiredPermissions={[UserPermission.CREATE_COMPANY]} redirectTo='/'>
                <CompaniesList />
              </HasPermission>
            }
          />
          <Route
            path=':companyId/'
            element={
              <HasPermission requiredPermissions={[UserPermission.CREATE_COMPANY]} redirectTo='/'>
                <CompanyDetail />
              </HasPermission>
            }
          />
        </Route>
        <Route path={appRoutes.wyreAIAdmin}>
          <Route
            index
            element={
              <WyreAIAdminGuard>
                <WyreAIAdmin />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='user-management'
            element={
              <WyreAIAdminGuard>
                <UserManagement />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='projects'
            element={
              <WyreAIAdminGuard>
                <Projects />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='metrics'
            element={
              <WyreAIAdminGuard>
                <Metrics />
              </WyreAIAdminGuard>
            }
          />
        </Route>
      </Route>
      <Route path='*' element={<NotFound />} />
    </Routes>
  );
};
