import React from 'react';
import { Card, Col, Row } from 'antd';
import { FaU<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaClipboardList } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { themeTokens } from 'src/theme/tokens';
import { appRoutes } from '../utils/constant';

const Container = styled.div`
  padding: 24px;
  background-color: ${themeTokens.whiteBg};
  min-height: calc(100vh - 60px);
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 32px;
`;

const StyledCard = styled(Card)`
  height: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${themeTokens.inputBorder};
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: ${themeTokens.primaryColor};
  }
  
  .ant-card-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
  }
`;

const IconWrapper = styled.div`
  font-size: 48px;
  color: ${themeTokens.primaryColor};
  margin-bottom: 16px;
`;

const TileTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 8px;
`;

const TileDescription = styled.p`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin: 0;
`;

const ComingSoonBadge = styled.div`
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: ${themeTokens.primaryColor};
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
`;

interface AdminTile {
  key: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  route?: string;
  comingSoon?: boolean;
}

const WyreAIAdmin: React.FC = () => {
  const navigate = useNavigate();

  const adminTiles: AdminTile[] = [
    {
      key: 'user-management',
      title: 'User Management',
      description: 'Manage companies, users, and license provisioning',
      icon: <FaUsers />,
      route: `/${appRoutes.wyreAIAdmin}/user-management`
    },
    {
      key: 'metrics',
      title: 'Metrics',
      description: 'View usage statistics such as active users, number of projects, document uploads, and more',
      icon: <FaChartBar />,
      comingSoon: true
    },
    {
      key: 'events',
      title: 'Events',
      description: 'Log and review platform-level activities and system events for auditing and troubleshooting',
      icon: <FaClipboardList />,
      comingSoon: true
    }
  ];

  const handleTileClick = (tile: AdminTile) => {
    if (tile.comingSoon) {
      return;
    }
    
    if (tile.route) {
      navigate(tile.route);
    }
  };

  return (
    <Container>
      <PageTitle>Wyre AI Admin</PageTitle>
      <Row gutter={[24, 24]}>
        {adminTiles.map((tile) => (
          <Col xs={24} sm={12} lg={8} key={tile.key}>
            <StyledCard 
              onClick={() => handleTileClick(tile)}
              style={{ 
                cursor: tile.comingSoon ? 'not-allowed' : 'pointer',
                opacity: tile.comingSoon ? 0.7 : 1
              }}
            >
              {tile.comingSoon && <ComingSoonBadge>Coming Soon</ComingSoonBadge>}
              <IconWrapper>{tile.icon}</IconWrapper>
              <TileTitle>{tile.title}</TileTitle>
              <TileDescription>{tile.description}</TileDescription>
            </StyledCard>
          </Col>
        ))}
      </Row>
    </Container>
  );
};

export default WyreAIAdmin;
