import { ArrowLeftOutlined, BuildOutlined, UserOutlined } from '@ant-design/icons';
import { Card, Col, Row, Statistic, Button, Spin } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import projectIcon from '../../assets/images/project.svg';
import { appRoutes } from '../utils/constant';

// Types
interface MetricItem {
  title: string;
  value: number;
}

interface MetricSection {
  title: string;
  icon: React.ReactNode;
  color: string;
  items: MetricItem[];
}

const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const SectionTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const BaseMetricCard = styled(Card)<{ borderColor: string; textColor: string }>`
  height: 140px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid ${props => props.borderColor};

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .ant-card-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
  }

  .ant-statistic-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .ant-statistic-content {
    font-size: 28px;
    font-weight: 700;
    color: ${props => props.textColor};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`;

const ProjectIcon = styled.img`
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(47%) sepia(96%) saturate(1237%) hue-rotate(195deg)
    brightness(98%) contrast(98%);
`;

// Styled Row component for consistent spacing
const MetricRow = styled(Row)`
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const Metrics: React.FC = () => {
  const navigate = useNavigate();

  // TODO: Replace with actual API calls when metrics endpoints are available
  // For now, using mock data
  const isLoading = false; // Set to true when implementing actual API calls

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  // Define metric sections with their data
  const metricSections: MetricSection[] = [
    {
      title: 'Companies',
      icon: <BuildOutlined style={{ color: themeTokens.successGreen }} />,
      color: themeTokens.successGreen,
      items: [
        { title: 'Total Companies', value: 45 },
        { title: 'Enterprise Companies', value: 12 },
        { title: 'Non-Enterprise Companies', value: 33 }
      ]
    },
    {
      title: 'Projects',
      icon: <ProjectIcon src={projectIcon} alt='Projects' />,
      color: themeTokens.infoBlue,
      items: [
        { title: 'Total Projects', value: 128 },
        { title: 'Paid Projects', value: 89 },
        { title: 'Unpaid Projects', value: 39 },
        { title: 'Projects in Bidding Phase', value: 23 },
        { title: 'Projects Closed (Won)', value: 67 },
        { title: 'Projects Closed (Lost)', value: 38 }
      ]
    },
    {
      title: 'Users',
      icon: <UserOutlined style={{ color: themeTokens.warningOrange }} />,
      color: themeTokens.warningOrange,
      items: [
        { title: 'Total Users', value: 234 },
        { title: 'Active Users', value: 198 },
        { title: 'Inactive Users', value: 36 }
      ]
    }
  ];

  if (isLoading) {
    return (
      <Container>
        <Header>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Metrics Dashboard</PageTitle>
        </Header>
        <LoadingContainer>
          <Spin size='large' />
        </LoadingContainer>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Metrics Dashboard</PageTitle>
      </Header>
      {metricSections.map((section, index) => (
        <>
          <SectionTitle>
            {section.icon}
            {section.title}
          </SectionTitle>
          <MetricRow
            gutter={[24, 24]}
            style={index === metricSections.length - 1 ? { marginBottom: 0 } : undefined}
          >
            {section.items.map((item, index) => (
              <Col xs={24} sm={8} key={index}>
                <BaseMetricCard borderColor={section.color} textColor={section.color}>
                  <Statistic title={item.title} value={item.value} />
                </BaseMetricCard>
              </Col>
            ))}
          </MetricRow>
        </>
      ))}
    </Container>
  );
};

export default Metrics;
