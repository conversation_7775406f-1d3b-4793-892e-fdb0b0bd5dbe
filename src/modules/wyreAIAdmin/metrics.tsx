import { ArrowLeftOutlined, BuildOutlined, ProjectOutlined, UserOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Card, Col, Row, Statistic, Button, Spin } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { projectAPI, userAPI, companyAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import { queryKeys, appRoutes } from '../utils/constant';

const Container = styled.div`
  padding: 24px;
  background-color: ${themeTokens.whiteBg};
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const SectionTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const MetricCard = styled(Card)`
  height: 140px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .ant-card-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
  }

  .ant-statistic-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .ant-statistic-content {
    font-size: 28px;
    font-weight: 700;
  }
`;

const CompanyCard = styled(MetricCard)`
  border-left: 4px solid #52c41a;

  .ant-statistic-content {
    color: #52c41a;
  }
`;

const ProjectCard = styled(MetricCard)`
  border-left: 4px solid #1890ff;

  .ant-statistic-content {
    color: #1890ff;
  }
`;

const UserCard = styled(MetricCard)`
  border-left: 4px solid #fa8c16;

  .ant-statistic-content {
    color: #fa8c16;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`;

interface MetricsData {
  companies: {
    total: number;
    enterprise: number;
    nonEnterprise: number;
  };
  projects: {
    total: number;
    paid: number;
    unpaid: number;
    bidding: number;
    won: number;
    lost: number;
  };
  users: {
    total: number;
    active: number;
    inactive: number;
  };
}

const Metrics: React.FC = () => {
  const navigate = useNavigate();

  // TODO: Replace with actual API calls when metrics endpoints are available
  // For now, using mock data
  const companiesData = null;
  const projectsData = null;
  const usersData = null;
  const companiesLoading = false;
  const projectsLoading = false;
  const usersLoading = false;

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const isLoading = companiesLoading || projectsLoading || usersLoading;

  // Mock data for demonstration (replace with actual API data)
  const metricsData: MetricsData = {
    companies: {
      total: companiesData?.total || 45,
      enterprise: companiesData?.enterprise || 12,
      nonEnterprise: companiesData?.nonEnterprise || 33
    },
    projects: {
      total: projectsData?.total || 128,
      paid: projectsData?.paid || 89,
      unpaid: projectsData?.unpaid || 39,
      bidding: projectsData?.bidding || 23,
      won: projectsData?.won || 67,
      lost: projectsData?.lost || 38
    },
    users: {
      total: usersData?.total || 234,
      active: usersData?.active || 198,
      inactive: usersData?.inactive || 36
    }
  };

  if (isLoading) {
    return (
      <Container>
        <Header>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Metrics Dashboard</PageTitle>
        </Header>
        <LoadingContainer>
          <Spin size='large' />
        </LoadingContainer>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Metrics Dashboard</PageTitle>
      </Header>

      {/* Companies Section */}
      <SectionTitle>
        <BuildOutlined style={{ color: '#52c41a' }} />
        Companies
      </SectionTitle>
      <Row gutter={[24, 24]} style={{ marginBottom: 40 }}>
        <Col xs={24} sm={8}>
          <CompanyCard>
            <Statistic
              title='Total Companies'
              value={metricsData.companies.total}
              suffix='companies'
            />
          </CompanyCard>
        </Col>
        <Col xs={24} sm={8}>
          <CompanyCard>
            <Statistic
              title='Enterprise Companies'
              value={metricsData.companies.enterprise}
              suffix='companies'
            />
          </CompanyCard>
        </Col>
        <Col xs={24} sm={8}>
          <CompanyCard>
            <Statistic
              title='Non-Enterprise Companies'
              value={metricsData.companies.nonEnterprise}
              suffix='companies'
            />
          </CompanyCard>
        </Col>
      </Row>

      {/* Projects Section */}
      <SectionTitle>
        <ProjectOutlined style={{ color: '#1890ff' }} />
        Projects
      </SectionTitle>
      <Row gutter={[24, 24]} style={{ marginBottom: 40 }}>
        <Col xs={24} sm={8}>
          <ProjectCard>
            <Statistic
              title='Total Projects'
              value={metricsData.projects.total}
              suffix='projects'
            />
          </ProjectCard>
        </Col>
        <Col xs={24} sm={8}>
          <ProjectCard>
            <Statistic title='Paid Projects' value={metricsData.projects.paid} suffix='projects' />
          </ProjectCard>
        </Col>
        <Col xs={24} sm={8}>
          <ProjectCard>
            <Statistic
              title='Unpaid Projects'
              value={metricsData.projects.unpaid}
              suffix='projects'
            />
          </ProjectCard>
        </Col>
        <Col xs={24} sm={8}>
          <ProjectCard>
            <Statistic
              title='Projects in Bidding Phase'
              value={metricsData.projects.bidding}
              suffix='projects'
            />
          </ProjectCard>
        </Col>
        <Col xs={24} sm={8}>
          <ProjectCard>
            <Statistic
              title='Projects Closed (Won)'
              value={metricsData.projects.won}
              suffix='projects'
            />
          </ProjectCard>
        </Col>
        <Col xs={24} sm={8}>
          <ProjectCard>
            <Statistic
              title='Projects Closed (Lost)'
              value={metricsData.projects.lost}
              suffix='projects'
            />
          </ProjectCard>
        </Col>
      </Row>

      {/* Users Section */}
      <SectionTitle>
        <UserOutlined style={{ color: '#fa8c16' }} />
        Users
      </SectionTitle>
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={8}>
          <UserCard>
            <Statistic title='Total Users' value={metricsData.users.total} suffix='users' />
          </UserCard>
        </Col>
        <Col xs={24} sm={8}>
          <UserCard>
            <Statistic title='Active Users' value={metricsData.users.active} suffix='users' />
          </UserCard>
        </Col>
        <Col xs={24} sm={8}>
          <UserCard>
            <Statistic title='Inactive Users' value={metricsData.users.inactive} suffix='users' />
          </UserCard>
        </Col>
      </Row>
    </Container>
  );
};

export default Metrics;
