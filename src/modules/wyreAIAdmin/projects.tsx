import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Select,
  Modal,
  Form,
  Popconfirm,
  Input,
  notification
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectDTO } from 'src/api';
import { projectAPI, userAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

import { queryKeys, appRoutes } from '../utils/constant';
import { extractErrorMessage } from '../utils/errorHandler';
import { formatDate } from '../utils/util';

const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const GlobalSearchContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: flex-start;
`;

const StyledTableContainer = styled.div`
  .ant-pagination {
    display: flex !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }
`;

const FilterDropdownContainer = styled.div`
  padding: 8px;
`;

const FilterInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

const FilterButtonContainer = styled(Space)`
  /* Styled Space component for filter buttons */
`;

const FilterButton = styled(Button)`
  width: 90px;
`;

const TooltipContent = styled.span`
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const CompanyTooltipContent = styled.span`
  display: block;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const GlobalSearchInput = styled(Input.Search)`
  width: 400px;
`;

const StyledTable = styled(Table<ExtendedProjectDTO>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /*Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;

interface ExtendedProjectDTO extends ProjectDTO {
  companyName?: string;
  projectNumber?: string;
  projectType?: string;
  subscriptionType?: string;
  subscriptionStatus?: string;
  bidCaptainName?: string;
  numberOfVersions?: number;
}

const Projects: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});
  const [changeOwnerModalVisible, setChangeOwnerModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ExtendedProjectDTO | null>(null);

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Fetch all projects with backend search and filtering
  const { data: projectsData, isLoading } = useQuery({
    queryKey: [
      queryKeys.allProjects,
      currentPage,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      projectAPI.getAllProjects(
        columnFilters.companyName || undefined, // companyId filter (will be updated when API supports it)
        debouncedSearchText || columnFilters.name || undefined, // name (global search + column filter)
        columnFilters.description || undefined, // description filter
        columnFilters.location || undefined, // location filter
        columnFilters.status || undefined, // status filter
        columnFilters.bidCaptainName || undefined, // owner filter (will be updated when API supports it)
        currentPage - 1, // page
        pageSize, // size
        [`${sortField},${sortOrder}`] // sort
      ),
    select: response => response.data
  });

  // Fetch users for bid captain selection
  const { data: usersData } = useQuery({
    queryKey: [queryKeys.usersList],
    queryFn: () => userAPI.getAllUsers(0, 1000),
    select: response => response.data
  });

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    if (filters) {
      const newFilters: Record<string, any> = {};
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key].length > 0) {
          newFilters[key] = filters[key][0]; // Take first filter value
        }
      });
      setColumnFilters(newFilters);
      setCurrentPage(1); // Reset to first page when filtering
    }

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  const handleChangeOwner = useCallback(
    (project: ExtendedProjectDTO) => {
      setSelectedProject(project);
      setChangeOwnerModalVisible(true);
      form.setFieldsValue({
        bidCaptain: project.bidCaptainId
      });
    },
    [form]
  );

  const handleChangeOwnerSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      if (selectedProject) {
        // API call to update project owner
        // await projectAPI.updateProject(selectedProject.id, { bidCaptainId: values.bidCaptain });
        setChangeOwnerModalVisible(false);
        setSelectedProject(null);
        form.resetFields();
      }
    } catch (error) {
      console.error('Failed to change owner:', error);
    }
  }, [form, selectedProject]);

  // Delete project mutation
  const { mutate: deleteProject } = useMutation({
    mutationFn: (projectId: number) => projectAPI.softDeleteProject(projectId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
      notification.success({ message: 'Project deleted successfully' });
    },
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Project deletion failed') });
    }
  });

  const handleDeleteProject = useCallback(
    (projectId: number) => {
      deleteProject(projectId);
    },
    [deleteProject]
  );

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : '#fff' }} />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined style={{ color: filtered ? '#1890ff' : '#fff' }} />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const columns: ColumnsType<ExtendedProjectDTO> = useMemo(
    () => [
      {
        title: 'Company Name',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('companyName', 'company name'),
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <CompanyTooltipContent>{companyName || '-'}</CompanyTooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Project Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'project name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Project Number',
        dataIndex: 'projectNumber',
        key: 'projectNumber',
        width: 120,
        render: (projectNumber: string) => projectNumber || '-'
      },
      {
        title: 'Project Type',
        dataIndex: 'projectType',
        key: 'projectType',
        width: 120,
        render: (projectType: string) => projectType || '-'
      },
      {
        title: 'Subscription',
        dataIndex: 'subscriptionType',
        key: 'subscriptionType',
        width: 120,
        render: (subscriptionType: string) => (
          <Tag color={subscriptionType === 'Enterprise' ? 'green' : 'blue'}>
            {subscriptionType || 'Non-Enterprise'}
          </Tag>
        )
      },
      {
        title: 'Subscription Status',
        dataIndex: 'subscriptionStatus',
        key: 'subscriptionStatus',
        width: 140,
        ...getColumnSelectProps('subscriptionStatus', [
          { text: 'Active', value: 'Active' },
          { text: 'Expired', value: 'Expired' },
          { text: 'Pending', value: 'Pending' }
        ]),
        render: (status: string) => {
          let color = 'orange';
          if (status === 'Active') {
            color = 'green';
          } else if (status === 'Expired') {
            color = 'red';
          }
          return <Tag color={color}>{status || 'Unknown'}</Tag>;
        }
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Bid Captain',
        dataIndex: 'bidCaptainName',
        key: 'bidCaptainName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('bidCaptainName', 'bid captain'),
        render: (bidCaptainName: string) => bidCaptainName || '-'
      },
      {
        title: 'Bid Due Date',
        dataIndex: 'endDate',
        key: 'endDate',
        width: 120,
        sorter: true,
        render: (endDate: string) => (endDate ? formatDate(endDate) : '-')
      },
      {
        title: 'Versions',
        dataIndex: 'numberOfVersions',
        key: 'numberOfVersions',
        width: 80,
        render: (numberOfVersions: number) => numberOfVersions || 0
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 100,
        render: (_, record) => (
          <Space size='small'>
            <EditOutlined
              className='action-icon'
              onClick={() => handleChangeOwner(record)}
              title='Change Owner'
            />
            <Popconfirm
              title='Delete Project'
              description='Are you sure you want to delete this project?'
              onConfirm={() => handleDeleteProject(record.projectId!)}
              okText='Yes, Delete'
              cancelText='Cancel'
              okType='danger'
              placement='left'
            >
              <DeleteOutlined
                className='action-icon'
                title='Delete Project'
                style={{ cursor: 'pointer', color: '#ff4d4f' }}
              />
            </Popconfirm>
          </Space>
        )
      }
    ],
    [handleChangeOwner, handleDeleteProject, getColumnSearchProps, getColumnSelectProps]
  );

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Projects</PageTitle>
        </HeaderLeft>
      </Header>

      <GlobalSearchContainer>
        <GlobalSearchInput
          placeholder='Search across all projects...'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          onSearch={handleGlobalSearch}
          onChange={e => {
            if (!e.target.value) {
              handleGlobalSearch('');
            }
          }}
        />
      </GlobalSearchContainer>

      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={projectsData?.content || []}
          rowKey='projectId'
          loading={isLoading}
          onChange={handleTableChange}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: projectsData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} projects`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1400 }}
        />
      </StyledTableContainer>

      {/* Change Owner Modal */}
      <Modal
        title='Change Project Owner'
        open={changeOwnerModalVisible}
        onOk={handleChangeOwnerSubmit}
        onCancel={() => {
          setChangeOwnerModalVisible(false);
          setSelectedProject(null);
          form.resetFields();
        }}
        okText='Change Owner'
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            label='New Bid Captain'
            name='bidCaptain'
            rules={[{ required: true, message: 'Please select a bid captain' }]}
          >
            <Select
              placeholder='Select bid captain'
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {usersData?.content?.map(user => (
                <Select.Option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Container>
  );
};

export default Projects;
