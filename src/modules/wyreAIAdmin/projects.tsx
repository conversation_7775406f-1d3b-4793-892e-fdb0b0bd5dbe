import React, { useState, useMemo, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Table, Input, Button, Space, Tag, Tooltip, Select, Modal, Form } from 'antd';
import {
  SearchOutlined,
  ArrowLeftOutlined,
  FilterOutlined,
  PlusOutlined,
  DownloadOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ColumnsType } from 'antd/es/table';
import styled from 'styled-components';
import { themeTokens } from 'src/theme/tokens';
import { projectAPI, userAPI } from 'src/api/apiClient';
import { ProjectDTO, UserDTO } from 'src/api';
import { queryKeys, appRoutes } from '../utils/constant';
import { formatDate } from '../utils/util';
import ExportFile from '../common/exportFile';

const Container = styled.div`
  padding: 24px;
  background-color: ${themeTokens.whiteBg};
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const HeaderRight = styled.div`
  display: flex;
  gap: 12px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const FiltersContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
`;

const StyledTable = styled(Table)`
  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textBlack};
    font-weight: 600;
  }
`;

// Extended ProjectDTO interface to include additional fields
interface ExtendedProjectDTO extends ProjectDTO {
  companyName?: string;
  projectNumber?: string;
  projectType?: string;
  subscriptionType?: string;
  subscriptionStatus?: string;
  bidCaptainName?: string;
  numberOfVersions?: number;
}

const Projects: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [companyFilter, setCompanyFilter] = useState<string | undefined>();
  const [subscriptionFilter, setSubscriptionFilter] = useState<string | undefined>();
  const [statusFilter, setStatusFilter] = useState<string | undefined>();
  const [changeOwnerModalVisible, setChangeOwnerModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ExtendedProjectDTO | null>(null);

  // Fetch all projects
  const { data: projectsData, isLoading } = useQuery({
    queryKey: [
      queryKeys.allProjects,
      currentPage,
      pageSize,
      searchText,
      companyFilter,
      subscriptionFilter,
      statusFilter
    ],
    queryFn: () =>
      projectAPI.getAllProjects(
        companyFilter ? Number(companyFilter) : undefined, // companyId
        searchText || undefined, // name
        undefined, // description
        undefined, // location
        statusFilter as any, // status
        undefined, // owner
        currentPage - 1, // page
        pageSize, // size
        ['createdAt,desc'] // sort
      ),
    select: response => response.data
  });

  // Fetch users for bid captain selection
  const { data: usersData } = useQuery({
    queryKey: [queryKeys.usersList],
    queryFn: () => userAPI.getAllUsers(0, 1000),
    select: response => response.data
  });

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const handleChangeOwner = (project: ExtendedProjectDTO) => {
    setSelectedProject(project);
    setChangeOwnerModalVisible(true);
    form.setFieldsValue({
      bidCaptain: project.bidCaptainId
    });
  };

  const handleChangeOwnerSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      if (selectedProject) {
        // API call to update project owner
        // await projectAPI.updateProject(selectedProject.id, { bidCaptainId: values.bidCaptain });
        setChangeOwnerModalVisible(false);
        setSelectedProject(null);
        form.resetFields();
      }
    } catch (error) {
      console.error('Failed to change owner:', error);
    }
  }, [form, selectedProject]);

  const handleCreateProject = () => {
    // Navigate to create project page or open modal
    navigate(`/${appRoutes.projects}/create`);
  };

  const exportColumns = [
    { label: 'Company Name', key: 'companyName' },
    { label: 'Project Name', key: 'name' },
    { label: 'Project Number', key: 'projectNumber' },
    { label: 'Project Type', key: 'projectType' },
    { label: 'Subscription', key: 'subscriptionType' },
    { label: 'Subscription Status', key: 'subscriptionStatus' },
    { label: 'Created Date', key: 'createdAt' },
    { label: 'Bid Captain', key: 'bidCaptainName' },
    { label: 'Bid Due Date', key: 'endDate' },
    { label: 'Number of Versions', key: 'numberOfVersions' }
  ];

  const columns: ColumnsType<ExtendedProjectDTO> = useMemo(
    () => [
      {
        title: 'Company Name',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 150,
        sorter: true,
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <span
              style={{
                display: 'block',
                maxWidth: '130px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {companyName || '-'}
            </span>
          </Tooltip>
        )
      },
      {
        title: 'Project Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        render: (name: string) => (
          <Tooltip title={name}>
            <span
              style={{
                display: 'block',
                maxWidth: '180px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {name || '-'}
            </span>
          </Tooltip>
        )
      },
      {
        title: 'Project Number',
        dataIndex: 'projectNumber',
        key: 'projectNumber',
        width: 120,
        render: (projectNumber: string) => projectNumber || '-'
      },
      {
        title: 'Project Type',
        dataIndex: 'projectType',
        key: 'projectType',
        width: 120,
        render: (projectType: string) => projectType || '-'
      },
      {
        title: 'Subscription',
        dataIndex: 'subscriptionType',
        key: 'subscriptionType',
        width: 120,
        render: (subscriptionType: string) => (
          <Tag color={subscriptionType === 'Enterprise' ? 'green' : 'blue'}>
            {subscriptionType || 'Non-Enterprise'}
          </Tag>
        )
      },
      {
        title: 'Subscription Status',
        dataIndex: 'subscriptionStatus',
        key: 'subscriptionStatus',
        width: 140,
        render: (status: string) => {
          const color = status === 'Active' ? 'green' : status === 'Expired' ? 'red' : 'orange';
          return <Tag color={color}>{status || 'Unknown'}</Tag>;
        }
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Bid Captain',
        dataIndex: 'bidCaptainName',
        key: 'bidCaptainName',
        width: 150,
        render: (bidCaptainName: string) => bidCaptainName || '-'
      },
      {
        title: 'Bid Due Date',
        dataIndex: 'endDate',
        key: 'endDate',
        width: 120,
        sorter: true,
        render: (endDate: string) => (endDate ? formatDate(endDate) : '-')
      },
      {
        title: 'Versions',
        dataIndex: 'numberOfVersions',
        key: 'numberOfVersions',
        width: 80,
        render: (numberOfVersions: number) => numberOfVersions || 0
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 100,
        render: (_, record) => (
          <Button
            type='text'
            icon={<EditOutlined />}
            onClick={() => handleChangeOwner(record)}
            title='Change Owner'
          />
        )
      }
    ],
    []
  );

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Projects</PageTitle>
        </HeaderLeft>
        <HeaderRight>
          <Button type='primary' icon={<PlusOutlined />} onClick={handleCreateProject}>
            Create Project
          </Button>
          <ExportFile
            data={projectsData?.content || []}
            columns={exportColumns}
            filename='projects'
          />
        </HeaderRight>
      </Header>

      <FiltersContainer>
        <Input.Search
          placeholder='Search projects...'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          style={{ width: 300 }}
          onSearch={handleSearch}
          onChange={e => {
            if (!e.target.value) {
              handleSearch('');
            }
          }}
        />

        <Select
          placeholder='Filter by Company'
          allowClear
          style={{ width: 200 }}
          onChange={setCompanyFilter}
        >
          {/* Add company options here */}
        </Select>

        <Select
          placeholder='Filter by Subscription'
          allowClear
          style={{ width: 180 }}
          onChange={setSubscriptionFilter}
        >
          <Select.Option value='Enterprise'>Enterprise</Select.Option>
          <Select.Option value='Non-Enterprise'>Non-Enterprise</Select.Option>
        </Select>

        <Select
          placeholder='Filter by Status'
          allowClear
          style={{ width: 150 }}
          onChange={setStatusFilter}
        >
          <Select.Option value='Active'>Active</Select.Option>
          <Select.Option value='Expired'>Expired</Select.Option>
          <Select.Option value='Pending'>Pending</Select.Option>
        </Select>
      </FiltersContainer>

      <StyledTable
        columns={columns}
        dataSource={projectsData?.content || []}
        rowKey='id'
        loading={isLoading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: projectsData?.totalElements || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} projects`,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size || 20);
          }
        }}
        scroll={{ x: 1400 }}
      />

      {/* Change Owner Modal */}
      <Modal
        title='Change Project Owner'
        open={changeOwnerModalVisible}
        onOk={handleChangeOwnerSubmit}
        onCancel={() => {
          setChangeOwnerModalVisible(false);
          setSelectedProject(null);
          form.resetFields();
        }}
        okText='Change Owner'
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            label='New Bid Captain'
            name='bidCaptain'
            rules={[{ required: true, message: 'Please select a bid captain' }]}
          >
            <Select
              placeholder='Select bid captain'
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {usersData?.content?.map(user => (
                <Select.Option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Container>
  );
};

export default Projects;
