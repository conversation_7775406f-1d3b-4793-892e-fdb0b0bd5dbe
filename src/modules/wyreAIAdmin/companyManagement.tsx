import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined,
  PlusOutlined,
  ExportOutlined,
  EyeOutlined,
  UserOutlined,
  ProjectOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Select,
  Modal,
  Form,
  Popconfirm,
  Input,
  notification
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CompanyDTO } from 'src/api';
import { companyAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

import { queryKeys, appRoutes, subscriptionStatusOptions } from '../utils/constant';
import { extractErrorMessage } from '../utils/errorHandler';
import { formatDate } from '../utils/util';

const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const GlobalSearchContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  gap: 12px;
`;

const StyledTable = styled(Table)`
  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
    border-bottom: 1px solid ${themeTokens.tableHeaderBg};
  }

  .ant-table-tbody > tr:hover > td {
    background-color: ${themeTokens.pageBg};
  }
`;

const StyledTableContainer = styled.div`
  .ant-pagination {
    display: flex !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }
`;

const FilterDropdownContainer = styled.div`
  padding: 8px;
`;

const FilterInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

const FilterButtonContainer = styled(Space)`
  /* Styled Space component for filter buttons */
`;

const FilterButton = styled(Button)`
  width: 90px;
`;

const TooltipContent = styled.span`
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const GlobalSearchInput = styled(Input.Search)`
  width: 400px;
`;

interface ExtendedCompanyDTO extends CompanyDTO {
  userCount?: number;
  projectCount?: number;
  lastActivity?: string;
}

const CompanyManagement: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<ExtendedCompanyDTO | null>(null);

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchText]);

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  // Fetch companies data
  const { data: companiesData, isLoading } = useQuery({
    queryKey: [
      queryKeys.companiesList,
      currentPage - 1,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      companyAPI.getAllCompanies(
        currentPage - 1,
        pageSize,
        sortField && sortOrder ? [`${sortField},${sortOrder}`] : undefined,
        debouncedSearchText || undefined,
        columnFilters.industry || undefined,
        columnFilters.subscriptionStatus || undefined
      ),
    select: response => response.data
  });

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    const newFilters: Record<string, any> = {};
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key].length > 0) {
        newFilters[key] = filters[key][0];
      }
    });
    setColumnFilters(newFilters);

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  const handleEditCompany = useCallback(
    (company: ExtendedCompanyDTO) => {
      setSelectedCompany(company);
      setEditModalVisible(true);
      // Pre-populate form with company data
      form.setFieldsValue({
        name: company.name,
        industry: company.industry,
        subscriptionStatus: company.subscriptionStatus,
        primaryDomain: company.primaryDomain
      });
    },
    [form]
  );

  const handleViewCompany = useCallback((company: ExtendedCompanyDTO) => {
    setSelectedCompany(company);
    setViewModalVisible(true);
  }, []);

  const handleEditModalClose = useCallback(() => {
    setEditModalVisible(false);
    setSelectedCompany(null);
    form.resetFields();
  }, [form]);

  const handleViewModalClose = useCallback(() => {
    setViewModalVisible(false);
    setSelectedCompany(null);
  }, []);

  const handleEditSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      if (selectedCompany) {
        // API call to update company
        // await companyAPI.updateCompany(selectedCompany.id, values);
        notification.success({ message: 'Company updated successfully' });
        handleEditModalClose();
        queryClient.invalidateQueries({ queryKey: [queryKeys.companiesList] });
      }
    } catch (error) {
      notification.error({
        message: extractErrorMessage(error, 'Failed to update company')
      });
    }
  }, [form, selectedCompany, queryClient, handleEditModalClose]);

  // Delete company mutation
  const { mutate: deleteCompany } = useMutation({
    mutationFn: (companyId: number) => companyAPI.deleteCompany(companyId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.companiesList] });
      notification.success({ message: 'Company deleted successfully' });
    },
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Company deletion failed') });
    }
  });

  const handleDeleteCompany = useCallback(
    (companyId: number) => {
      deleteCompany(companyId);
    },
    [deleteCompany]
  );

  const handleCreateCompany = () => {
    // Navigate to create company or open modal
    console.log('Create company');
  };

  const handleExportData = () => {
    // Export functionality
    console.log('Export data');
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getSubscriptionStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return themeTokens.successGreen;
      case 'TRIAL':
        return themeTokens.warningOrange;
      case 'SUSPENDED':
        return themeTokens.dangerRed;
      default:
        return themeTokens.textGray;
    }
  };

  const columns: ColumnsType<ExtendedCompanyDTO> = useMemo(
    () => [
      {
        title: 'Company ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true
      },
      {
        title: 'Company Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'company name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Industry',
        dataIndex: 'industry',
        key: 'industry',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('industry', 'industry'),
        render: (industry: string) => industry || '-'
      },
      {
        title: 'Subscription Status',
        dataIndex: 'subscriptionStatus',
        key: 'subscriptionStatus',
        width: 150,
        ...getColumnSelectProps('subscriptionStatus', subscriptionStatusOptions),
        render: (status: string) => (
          <Tag color={getSubscriptionStatusColor(status)}>{status || 'Unknown'}</Tag>
        )
      },
      {
        title: 'Primary Domain',
        dataIndex: 'primaryDomain',
        key: 'primaryDomain',
        width: 180,
        render: (domain: string) => (
          <Tooltip title={domain}>
            <TooltipContent>{domain || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Users',
        dataIndex: 'userCount',
        key: 'userCount',
        width: 80,
        render: (userCount: number) => userCount || 0
      },
      {
        title: 'Projects',
        dataIndex: 'projectCount',
        key: 'projectCount',
        width: 80,
        render: (projectCount: number) => projectCount || 0
      },
      {
        title: 'Last Activity',
        dataIndex: 'lastActivity',
        key: 'lastActivity',
        width: 120,
        render: (lastActivity: string) => (lastActivity ? formatDate(lastActivity) : '-')
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 120,
        render: (_, record) => (
          <Space size='small'>
            <Tooltip title='View Details'>
              <EyeOutlined
                className='action-icon'
                onClick={() => handleViewCompany(record)}
                style={{ cursor: 'pointer', color: themeTokens.infoBlue }}
              />
            </Tooltip>
            <Tooltip title='Edit Company'>
              <EditOutlined
                className='action-icon'
                onClick={() => handleEditCompany(record)}
                style={{ cursor: 'pointer' }}
              />
            </Tooltip>
            <Popconfirm
              title='Delete Company'
              description='Are you sure you want to delete this company?'
              onConfirm={() => handleDeleteCompany(record.id!)}
              okText='Yes, Delete'
              cancelText='Cancel'
              okType='danger'
              placement='left'
            >
              <Tooltip title='Delete Company'>
                <DeleteOutlined
                  className='action-icon'
                  style={{ cursor: 'pointer', color: themeTokens.primaryColor }}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        )
      }
    ],
    [
      getColumnSearchProps,
      getColumnSelectProps,
      handleViewCompany,
      handleEditCompany,
      handleDeleteCompany
    ]
  );

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Company Management</PageTitle>
      </Header>

      <ActionBar>
        <GlobalSearchContainer>
          <GlobalSearchInput
            placeholder='Search across all companies...'
            allowClear
            enterButton={<SearchOutlined />}
            size='large'
            onSearch={handleGlobalSearch}
            onChange={e => {
              if (!e.target.value) {
                handleGlobalSearch('');
              }
            }}
          />
        </GlobalSearchContainer>

        <ActionButtonsContainer>
          <Button type='primary' icon={<PlusOutlined />} onClick={handleCreateCompany}>
            Create Company
          </Button>
          <Button icon={<ExportOutlined />} onClick={handleExportData}>
            Export Data
          </Button>
        </ActionButtonsContainer>
      </ActionBar>

      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={companiesData?.content || []}
          rowKey='id'
          loading={isLoading}
          onChange={handleTableChange}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: companiesData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} companies`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1400 }}
        />
      </StyledTableContainer>

      {/* View Company Details Modal */}
      <Modal
        title='Company Details'
        open={viewModalVisible}
        onCancel={handleViewModalClose}
        footer={[
          <Button key='close' onClick={handleViewModalClose}>
            Close
          </Button>
        ]}
        width={600}
      >
        {selectedCompany && (
          <div style={{ padding: '16px 0' }}>
            <Space direction='vertical' size='large' style={{ width: '100%' }}>
              <div>
                <h3 style={{ margin: '0 0 8px 0', color: themeTokens.textBlack }}>
                  <FaBuilding style={{ marginRight: 8 }} />
                  {selectedCompany.name}
                </h3>
                <Tag color={getSubscriptionStatusColor(selectedCompany.subscriptionStatus || '')}>
                  {selectedCompany.subscriptionStatus || 'Unknown'}
                </Tag>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <strong>Company ID:</strong>
                  <div>{selectedCompany.id}</div>
                </div>
                <div>
                  <strong>Industry:</strong>
                  <div>{selectedCompany.industry || '-'}</div>
                </div>
                <div>
                  <strong>Primary Domain:</strong>
                  <div>{selectedCompany.primaryDomain || '-'}</div>
                </div>
                <div>
                  <strong>Created Date:</strong>
                  <div>
                    {selectedCompany.createdAt ? formatDate(selectedCompany.createdAt) : '-'}
                  </div>
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div
                  style={{
                    textAlign: 'center',
                    padding: '16px',
                    backgroundColor: themeTokens.pageBg,
                    borderRadius: '8px'
                  }}
                >
                  <UserOutlined
                    style={{ fontSize: '24px', color: themeTokens.infoBlue, marginBottom: '8px' }}
                  />
                  <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                    {selectedCompany.userCount || 0}
                  </div>
                  <div style={{ color: themeTokens.textGray }}>Users</div>
                </div>
                <div
                  style={{
                    textAlign: 'center',
                    padding: '16px',
                    backgroundColor: themeTokens.pageBg,
                    borderRadius: '8px'
                  }}
                >
                  <ProjectOutlined
                    style={{
                      fontSize: '24px',
                      color: themeTokens.successGreen,
                      marginBottom: '8px'
                    }}
                  />
                  <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                    {selectedCompany.projectCount || 0}
                  </div>
                  <div style={{ color: themeTokens.textGray }}>Projects</div>
                </div>
              </div>

              {selectedCompany.secondaryDomains && selectedCompany.secondaryDomains.length > 0 && (
                <div>
                  <strong>Secondary Domains:</strong>
                  <div style={{ marginTop: '8px' }}>
                    {selectedCompany.secondaryDomains.map((domain, index) => (
                      <Tag key={index} style={{ margin: '2px' }}>
                        {domain}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Space>
          </div>
        )}
      </Modal>

      {/* Edit Company Modal */}
      <Modal
        title='Edit Company'
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={handleEditModalClose}
        okText='Save Changes'
        cancelText='Cancel'
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            name='name'
            label='Company Name'
            rules={[{ required: true, message: 'Please enter company name' }]}
          >
            <Input placeholder='Enter company name' />
          </Form.Item>
          <Form.Item name='industry' label='Industry'>
            <Input placeholder='Enter industry' />
          </Form.Item>
          <Form.Item name='subscriptionStatus' label='Subscription Status'>
            <Select placeholder='Select subscription status'>
              {subscriptionStatusOptions.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  {option.text}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name='primaryDomain' label='Primary Domain'>
            <Input placeholder='Enter primary domain' />
          </Form.Item>
        </Form>
      </Modal>
    </Container>
  );
};

export default CompanyManagement;
