import { SearchOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Table, Input, Button, Space, Tag, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserDTO } from 'src/api';
import { userAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import { queryKeys, appRoutes } from '../utils/constant';
import { formatDate } from '../utils/util';

// Extended UserDTO interface to include createdAt field
interface ExtendedUserDTO extends UserDTO {
  createdAt?: string;
}

const Container = styled.div`
  padding: 24px;
  /* background-color: ${themeTokens.whiteBg}; */
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const SearchContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
`;

const StyledTable = styled(Table<ExtendedUserDTO>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /*Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;

const UserManagement: React.FC = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Fetch all users across the platform
  const {
    data: usersData,
    isLoading,
    error
  } = useQuery({
    queryKey: [queryKeys.usersList, currentPage, pageSize, searchText],
    queryFn: () =>
      userAPI.getAllUsers(
        currentPage - 1,
        pageSize,
        ['createdAt,desc'],
        searchText || undefined,
        undefined,
        undefined,
        undefined,
        undefined
      ),
    select: response => response.data
  });

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const columns: ColumnsType<ExtendedUserDTO> = useMemo(
    () => [
      {
        title: 'User ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true
      },
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        render: (name: string) => (
          <Tooltip title={name}>
            <span
              style={{
                display: 'block',
                maxWidth: '180px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {name || '-'}
            </span>
          </Tooltip>
        )
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
        width: 250,
        sorter: true,
        render: (email: string) => (
          <Tooltip title={email}>
            <span
              style={{
                display: 'block',
                maxWidth: '230px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {email || '-'}
            </span>
          </Tooltip>
        )
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 200,
        sorter: true,
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <span
              style={{
                display: 'block',
                maxWidth: '180px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {companyName || '-'}
            </span>
          </Tooltip>
        )
      },
      {
        title: 'Active User',
        dataIndex: 'isActive',
        key: 'isActive',
        width: 120,
        sorter: true,
        render: (isActive: boolean) => (
          <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Yes' : 'No'}</Tag>
        )
      },
      {
        title: 'Create Time',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Current Role',
        dataIndex: ['role', 'name'],
        key: 'role',
        width: 150,
        render: (roleName: string) => <Tag color='blue'>{roleName || '-'}</Tag>
      },
      {
        title: 'Last Login',
        dataIndex: 'lastLoginDate',
        key: 'lastLoginDate',
        width: 150,
        render: (lastLoginDate: string) => (lastLoginDate ? formatDate(lastLoginDate) : '-')
      }
    ],
    []
  );

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>User Management</PageTitle>
      </Header>

      <SearchContainer>
        <Input.Search
          placeholder='Search by name or email'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          style={{ width: 400 }}
          onSearch={handleSearch}
          onChange={e => {
            if (!e.target.value) {
              handleSearch('');
            }
          }}
        />
      </SearchContainer>

      <StyledTable
        columns={columns}
        dataSource={usersData?.content || []}
        rowKey='id'
        loading={isLoading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: usersData?.totalElements || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} users`,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size || 20);
          }
        }}
        scroll={{ x: 1200 }}
      />
    </Container>
  );
};

export default UserManagement;
