import React from 'react';
import { Card, Typography, Empty, Table, Tag } from 'antd';
import { FileTextOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

const { Title } = Typography;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  font-family: 'Inter';
`;

const Content = styled.div`
  width: 95%;
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const Header = styled.div`
  font-weight: 400;
  font-size: 32px;
  line-height: 28px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
`;

const ComingSoonCard = styled(Card)`
  border-radius: 12px;
  text-align: center;
  padding: 40px;
  border: 2px dashed #d9d9d9;
`;

const StyledTable = styled(Table)`
  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: white;
    font-weight: 600;
  }
`;

const Events: React.FC = () => {
  // Mock events data - replace with actual API calls
  const mockEvents = [
    {
      id: 1,
      timestamp: '2024-01-20 14:30:25',
      event: 'User Login',
      user: '<EMAIL>',
      details: 'Successful login from IP *************',
      severity: 'info'
    },
    {
      id: 2,
      timestamp: '2024-01-20 14:25:12',
      event: 'Project Created',
      user: '<EMAIL>',
      details: 'New project "Office Building A" created',
      severity: 'success'
    },
    {
      id: 3,
      timestamp: '2024-01-20 14:20:45',
      event: 'Failed Login Attempt',
      user: '<EMAIL>',
      details: 'Multiple failed login attempts detected',
      severity: 'warning'
    }
  ];

  const columns = [
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180
    },
    {
      title: 'Event',
      dataIndex: 'event',
      key: 'event',
      width: 150
    },
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user',
      width: 200
    },
    {
      title: 'Details',
      dataIndex: 'details',
      key: 'details',
      width: 300
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => {
        const colors = {
          info: 'blue',
          success: 'green',
          warning: 'orange',
          error: 'red'
        };
        return <Tag color={colors[severity as keyof typeof colors]}>{severity.toUpperCase()}</Tag>;
      }
    }
  ];

  return (
    <Container>
      <Content>
        <Header>
          <FileTextOutlined style={{ color: themeTokens.primaryColor }} />
          Platform Events
        </Header>

        <Card title="Recent Events" extra={<InfoCircleOutlined />}>
          <StyledTable
            columns={columns}
            dataSource={mockEvents}
            pagination={{ pageSize: 10 }}
            rowKey="id"
            size="small"
          />
        </Card>

        <ComingSoonCard>
          <Empty
            image={<FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
            description={
              <div>
                <Title level={4} type="secondary">Advanced Event Logging Coming Soon</Title>
                <p style={{ color: themeTokens.textGray }}>
                  Comprehensive audit trails, event filtering, and real-time monitoring will be available here.
                </p>
              </div>
            }
          />
        </ComingSoonCard>
      </Content>
    </Container>
  );
};

export default Events;
