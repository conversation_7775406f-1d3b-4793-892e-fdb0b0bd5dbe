import React from 'react';
import { Navigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import { useWyreAIAdminAccess } from '../utils/permissions';
import { appRoutes } from '../utils/constant';

interface WyreAIAdminGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const WyreAIAdminGuard: React.FC<WyreAIAdminGuardProps> = ({ 
  children, 
  redirectTo = `/${appRoutes.projects}` 
}) => {
  const { hasAccess } = useWyreAIAdminAccess();

  if (!hasAccess) {
    return (
      <Result
        status="403"
        title="403"
        subTitle="Sorry, you are not authorized to access this page. Only Wyre AI employees with Super Admin or Project Creator roles can access the Wyre AI Admin section."
        extra={
          <Button type="primary" onClick={() => window.location.href = redirectTo}>
            Back Home
          </Button>
        }
      />
    );
  }

  return <>{children}</>;
};

export default WyreAIAdminGuard;
